#!/usr/bin/env python3
"""
更新 Dify 实例配置的脚本
"""

import asyncio
import aiohttp
import json
from motor.motor_asyncio import AsyncIOMotorClient

async def update_dify_config():
    """更新 Dify 实例配置"""
    
    print("🔧 更新 Dify 实例配置...")
    
    # 根据日志分析，正确的地址应该是
    new_base_url = "http://***************:1080/v1"
    print(f"将更新为正确的 Dify API 地址: {new_base_url}")
    
    if not new_base_url:
        print("❌ 未提供 URL，退出")
        return
    
    try:
        # 连接 MongoDB
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.thunderhub
        collection = db.dify_instances
        
        # 查找现有实例
        cursor = collection.find({})
        instances = []
        async for doc in cursor:
            instances.append(doc)
        
        if not instances:
            print("❌ 没有找到 Dify 实例")
            await client.close()
            return
        
        print(f"📋 找到 {len(instances)} 个 Dify 实例:")
        for i, instance in enumerate(instances, 1):
            print(f"  {i}. {instance.get('name', 'Unknown')} - {instance.get('base_url', 'Unknown')}")
        
        # 更新所有实例的 base_url
        for instance in instances:
            old_url = instance.get('base_url', '')
            instance_id = instance['_id']
            
            result = await collection.update_one(
                {'_id': instance_id},
                {
                    '$set': {
                        'base_url': new_base_url,
                        'status': 'inactive',  # 重置状态，需要重新测试
                        'last_error': None
                    }
                }
            )
            
            if result.modified_count > 0:
                print(f"✅ 更新实例 {instance.get('name', 'Unknown')}: {old_url} -> {new_base_url}")
            else:
                print(f"❌ 更新实例 {instance.get('name', 'Unknown')} 失败")
        
        await client.close()
        
        # 测试新的 URL
        print(f"\n🧪 测试新的 API 地址: {new_base_url}")
        await test_dify_api(new_base_url)
        
    except Exception as e:
        print(f"❌ 更新配置失败: {str(e)}")

async def test_dify_api(base_url):
    """测试 Dify API"""
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
        try:
            # 测试健康检查端点
            test_endpoints = [
                "",  # 根路径
                "/health",  # 健康检查
                "/docs",  # API 文档
                "/openapi.json",  # OpenAPI 规范
            ]
            
            for endpoint in test_endpoints:
                url = base_url.rstrip('/') + endpoint
                try:
                    async with session.get(url) as response:
                        print(f"   {endpoint or '/'}: {response.status}")
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')
                            if 'application/json' in content_type:
                                try:
                                    data = await response.json()
                                    print(f"     ✅ JSON 响应: {data}")
                                except:
                                    pass
                except:
                    pass
                    
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(update_dify_config())
