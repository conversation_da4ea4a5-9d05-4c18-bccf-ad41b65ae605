#!/usr/bin/env python3
"""
测试完整的 Dify 集成流程
"""

import asyncio
import aiohttp
import json

async def test_complete_flow():
    """测试完整的 Dify 集成流程"""
    
    backend_url = "http://localhost:8000"
    dify_url = "http://***************:1080"
    
    print("🧪 测试完整的 Dify 集成流程...")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 测试 Dify API 直接访问
        print("\n1️⃣ 测试 Dify API 直接访问...")
        try:
            async with session.get(f"{dify_url}/v1/parameters") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ Dify API 可访问: {response.status}")
                    print(f"   📊 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                else:
                    print(f"   ❌ Dify API 不可访问: {response.status}")
        except Exception as e:
            print(f"   ❌ Dify API 连接失败: {str(e)}")
        
        # 2. 测试 ThunderHub 后端 API
        print("\n2️⃣ 测试 ThunderHub 后端 API...")
        try:
            async with session.get(f"{backend_url}/api/v1/dify/instances") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 后端 API 可访问: {response.status}")
                    print(f"   📊 实例数量: {len(data.get('data', []))}")
                    
                    instances = data.get('data', [])
                    for i, instance in enumerate(instances, 1):
                        print(f"   {i}. {instance.get('name', 'Unknown')}")
                        print(f"      URL: {instance.get('base_url', 'Unknown')}")
                        print(f"      状态: {instance.get('status', 'Unknown')}")
                else:
                    print(f"   ❌ 后端 API 不可访问: {response.status}")
        except Exception as e:
            print(f"   ❌ 后端 API 连接失败: {str(e)}")
        
        # 3. 测试连接测试 API
        print("\n3️⃣ 测试连接测试 API...")
        test_data = {
            "base_url": f"{dify_url}/v1",
            "api_key": "test-key",
            "timeout": 30
        }
        
        try:
            async with session.post(
                f"{backend_url}/api/v1/dify/instances/test",
                json=test_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"   ✅ 连接测试 API 可访问: {response.status}")
                    print(f"   📊 测试结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get('success'):
                        print(f"   🎉 连接测试成功!")
                    else:
                        print(f"   ⚠️ 连接测试失败: {data.get('message', 'Unknown')}")
                else:
                    print(f"   ❌ 连接测试 API 失败: {response.status}")
                    error_text = await response.text()
                    print(f"   错误信息: {error_text}")
        except Exception as e:
            print(f"   ❌ 连接测试失败: {str(e)}")
        
        # 4. 测试前端 API 格式
        print("\n4️⃣ 测试前端 API 格式兼容性...")
        print("   根据前端代码分析:")
        print("   - 前端期望: response.success")
        print("   - 后端返回: {success: true, message: '...', ...}")
        print("   - request.ts 会直接返回后端数据")
        print("   ✅ 格式兼容性正常")
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("1. 确保 Dify 服务在 http://***************:1080 运行")
    print("2. 确保 ThunderHub 后端在 http://localhost:8000 运行")
    print("3. 前端应该能正常显示和测试 Dify 实例")
    print("4. 如果还有问题，请检查浏览器控制台的详细错误信息")

if __name__ == "__main__":
    asyncio.run(test_complete_flow())
