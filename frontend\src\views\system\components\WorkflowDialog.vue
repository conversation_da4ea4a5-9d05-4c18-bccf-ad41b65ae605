<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑工作流配置' : '添加工作流配置'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="workflow-form"
    >
      <el-form-item label="所属Dify服务" prop="instance_id">
        <el-select
          v-model="form.instance_id"
          placeholder="选择Dify服务器"
          style="width: 100%"
        >
          <el-option
            v-for="instance in instances"
            :key="instance.id"
            :label="instance.name"
            :value="instance.id"
          />
        </el-select>
        <div v-if="instances.length === 0" class="form-tip" style="color: #f56c6c;">
          暂无可用的Dify实例，请先在"Dify服务管理"标签页中添加实例
        </div>
        <div class="form-tip">
          选择这个工作流所在的Dify服务器（可以随时更改）
        </div>
      </el-form-item>

      <el-form-item label="工作流名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入工作流名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="工作流ID" prop="workflow_id">
        <el-input
          v-model="form.workflow_id"
          placeholder="请输入Dify中的App ID"
          maxlength="100"
        />
        <div class="form-tip">
          在Dify控制台中，每个App都有一个唯一的ID，通常在App设置页面可以找到
        </div>
      </el-form-item>

      <!-- APP配置部分 -->
      <el-divider content-position="left">APP配置</el-divider>

      <el-form-item label="APP名称">
        <el-input
          v-model="form.app_name"
          placeholder="请输入APP名称（可选）"
          maxlength="50"
        />
        <div class="form-tip">
          用于标识这个工作流使用的Dify应用，便于管理
        </div>
      </el-form-item>

      <el-form-item label="APP ID">
        <el-input
          v-model="form.app_id"
          placeholder="请输入Dify APP ID（可选）"
          maxlength="100"
        />
        <div class="form-tip">
          Dify应用的唯一标识符
        </div>
      </el-form-item>

      <el-form-item label="APP秘钥">
        <el-input
          v-model="form.app_secret"
          type="password"
          placeholder="请输入APP秘钥（可选）"
          show-password
          maxlength="200"
        />
        <div class="form-tip">
          从Dify控制台的"API管理"页面获取的APP专用秘钥（可选，如果不填写将使用实例级别的API密钥）
        </div>
      </el-form-item>

      <el-form-item label="工作流类型" prop="workflow_type">
        <el-select
          v-model="form.workflow_type"
          placeholder="选择工作流类型"
          style="width: 100%"
        >
          <el-option label="聊天机器人" value="chatbot" />
          <el-option label="工作流" value="workflow" />
          <el-option label="智能体" value="agent" />
          <el-option label="文本补全" value="completion" />
        </el-select>
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入工作流描述（可选）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="添加标签（可选）"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-divider content-position="left">参数映射配置</el-divider>

      <el-form-item label="输入参数映射">
        <div class="mapping-container">
          <div class="mapping-header">
            <span>源参数</span>
            <span>目标参数</span>
            <span>数据类型</span>
            <span>必填</span>
            <span>操作</span>
          </div>
          <div 
            v-for="(mapping, index) in form.input_mappings" 
            :key="`input-${index}`"
            class="mapping-row"
          >
            <el-input
              v-model="mapping.source_key"
              placeholder="源参数名"
              size="small"
            />
            <el-input
              v-model="mapping.target_key"
              placeholder="目标参数名"
              size="small"
            />
            <el-select
              v-model="mapping.data_type"
              placeholder="类型"
              size="small"
            >
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔" value="boolean" />
              <el-option label="对象" value="object" />
              <el-option label="数组" value="array" />
            </el-select>
            <el-checkbox v-model="mapping.required" />
            <el-button
              size="small"
              type="danger"
              @click="removeInputMapping(index)"
            >
              删除
            </el-button>
          </div>
          <el-button
            size="small"
            type="primary"
            @click="addInputMapping"
            class="add-mapping-btn"
          >
            添加输入映射
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="输出参数映射">
        <div class="mapping-container">
          <div class="mapping-header">
            <span>源参数</span>
            <span>目标参数</span>
            <span>数据类型</span>
            <span>必填</span>
            <span>操作</span>
          </div>
          <div 
            v-for="(mapping, index) in form.output_mappings" 
            :key="`output-${index}`"
            class="mapping-row"
          >
            <el-input
              v-model="mapping.source_key"
              placeholder="源参数名"
              size="small"
            />
            <el-input
              v-model="mapping.target_key"
              placeholder="目标参数名"
              size="small"
            />
            <el-select
              v-model="mapping.data_type"
              placeholder="类型"
              size="small"
            >
              <el-option label="字符串" value="string" />
              <el-option label="数字" value="number" />
              <el-option label="布尔" value="boolean" />
              <el-option label="对象" value="object" />
              <el-option label="数组" value="array" />
            </el-select>
            <el-checkbox v-model="mapping.required" />
            <el-button
              size="small"
              type="danger"
              @click="removeOutputMapping(index)"
            >
              删除
            </el-button>
          </div>
          <el-button
            size="small"
            type="primary"
            @click="addOutputMapping"
            class="add-mapping-btn"
          >
            添加输出映射
          </el-button>
        </div>
      </el-form-item>

      <el-divider content-position="left">高级配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="超时时间" prop="timeout">
            <el-input-number
              v-model="form.timeout"
              :min="1"
              :max="600"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">秒</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最大重试" prop="max_retries">
            <el-input-number
              v-model="form.max_retries"
              :min="0"
              :max="10"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">次</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重试延迟" prop="retry_delay">
            <el-input-number
              v-model="form.retry_delay"
              :min="0.1"
              :max="60"
              :step="0.1"
              :precision="1"
              style="width: 100%"
            />
            <div class="form-tip">秒</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-checkbox v-model="form.enabled">启用此工作流</el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSave"
          :loading="saving"
        >
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import difyApi, { type DifyWorkflow, type DifyInstance, type DifyParameterMapping } from '@/api/dify'

// Props
interface Props {
  modelValue: boolean
  workflow?: DifyWorkflow | null
  instances: DifyInstance[]
}

const props = withDefaults(defineProps<Props>(), {
  workflow: null,
  instances: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'saved': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const selectedInstanceId = ref('')

// 表单数据
const form = reactive<DifyWorkflow>({
  name: '',
  workflow_id: '',
  workflow_type: 'workflow',
  description: '',
  instance_id: '',
  // APP配置字段
  app_id: '',
  app_name: '',
  app_secret: '',
  input_mappings: [],
  output_mappings: [],
  tags: [],
  timeout: 60,
  max_retries: 3,
  retry_delay: 1.0,
  enabled: true
})

// 常用标签
const commonTags = [
  '文本生成',
  '图像处理',
  '数据分析',
  '内容创作',
  '客服机器人',
  '自动化',
  '翻译',
  '摘要'
]

// 表单验证规则
const rules: FormRules = {
  instance_id: [
    { required: true, message: '请选择Dify实例', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入工作流名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  workflow_id: [
    { required: true, message: '请输入工作流ID', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  workflow_type: [
    { required: true, message: '请选择工作流类型', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.workflow?.id)

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    workflow_id: '',
    workflow_type: 'workflow',
    description: '',
    instance_id: '',
    // APP配置字段
    app_id: '',
    app_name: '',
    app_secret: '',
    input_mappings: [],
    output_mappings: [],
    tags: [],
    timeout: 60,
    max_retries: 3,
    retry_delay: 1.0,
    enabled: true
  })
  selectedInstanceId.value = ''
  formRef.value?.clearValidate()
}

// 监听工作流变化
watch(() => props.workflow, (newWorkflow) => {
  if (newWorkflow) {
    Object.assign(form, {
      ...newWorkflow,
      input_mappings: newWorkflow.input_mappings || [],
      output_mappings: newWorkflow.output_mappings || [],
      tags: newWorkflow.tags || []
    })
  } else {
    resetForm()
  }
}, { immediate: true })

const createEmptyMapping = (): DifyParameterMapping => ({
  source_key: '',
  target_key: '',
  data_type: 'string',
  required: false
})

const addInputMapping = () => {
  form.input_mappings!.push(createEmptyMapping())
}

const removeInputMapping = (index: number) => {
  form.input_mappings!.splice(index, 1)
}

const addOutputMapping = () => {
  form.output_mappings!.push(createEmptyMapping())
}

const removeOutputMapping = (index: number) => {
  form.output_mappings!.splice(index, 1)
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
  } catch {
    return
  }

  saving.value = true
  try {
    if (isEdit.value) {
      await difyApi.workflow.update(props.workflow!.id!, form)
      ElMessage.success('工作流配置更新成功')
    } else {
      await difyApi.workflow.create(form.instance_id, form)
      ElMessage.success('工作流配置创建成功')
    }

    emit('saved')
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error(error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.workflow-form {
  padding: 0 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.mapping-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.mapping-header {
  display: grid;
  grid-template-columns: 1fr 1fr 100px 60px 80px;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 12px;
  color: #606266;
  padding: 0 4px;
}

.mapping-row {
  display: grid;
  grid-template-columns: 1fr 1fr 100px 60px 80px;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.add-mapping-btn {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
