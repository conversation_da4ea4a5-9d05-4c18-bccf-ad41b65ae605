# Dify集成概念说明

## 核心概念

### 🏢 Dify服务 (Dify Instance)
**定义**: 一个完整的Dify服务器部署

**包含内容**:
- **API地址**: Dify服务器的API端点 (如 `https://api.dify.ai` 或 `http://your-server.com/v1`)
- **API密钥**: 用于认证的密钥，从Dify控制台的"API管理"页面获取
- **连接配置**: 超时时间、重试次数等连接参数

**使用场景**:
- 连接到不同环境的Dify服务器 (开发、测试、生产)
- 连接到自部署的Dify实例
- 连接到Dify云服务

**示例**:
```
服务名称: 生产环境Dify
API地址: https://api.dify.ai/v1
API密钥: app-xxxxxxxxxxxxxxxxx

服务名称: 本地测试环境
API地址: http://192.168.1.100:1080/v1
API密钥: app-yyyyyyyyyyyyyyyyy
```

### 📱 工作流配置 (Workflow Configuration)
**定义**: Dify服务中具体App的配置信息

**包含内容**:
- **工作流ID**: 对应Dify中的App ID
- **工作流类型**: chatbot(聊天机器人)、workflow(工作流)、agent(智能体)、completion(文本补全)
- **输入输出映射**: 定义如何传递参数和处理结果
- **执行配置**: 超时时间、重试策略等

**关系**: 
- 一个Dify服务可以包含多个工作流配置
- 每个工作流配置对应Dify中的一个具体App

**示例**:
```
所属Dify服务: 生产环境Dify
工作流名称: 视频标题生成器
工作流ID: 9ex1IUTxrDSFoZdf (这是Dify中App的ID)
工作流类型: workflow
```

## 架构关系

```
ThunderHub
├── Dify服务1 (生产环境)
│   ├── 工作流A (标题生成)
│   ├── 工作流B (内容总结)
│   └── 工作流C (SEO优化)
├── Dify服务2 (测试环境)
│   ├── 工作流A (标题生成-测试版)
│   └── 工作流D (新功能测试)
└── Dify服务3 (本地开发)
    └── 工作流E (开发调试)
```

## 配置流程

### 1. 添加Dify服务
1. 在"Dify服务管理"标签页点击"添加Dify服务"
2. 填写服务名称 (如"生产环境"、"测试环境")
3. 填写API地址 (Dify服务器的API端点)
4. 填写API密钥 (从Dify控制台获取)
5. 测试连接确保配置正确

### 2. 配置工作流
1. 在"工作流配置"标签页点击"添加工作流"
2. 选择所属的Dify服务
3. 填写工作流名称 (便于识别的名称)
4. 填写工作流ID (Dify中App的ID)
5. 选择工作流类型
6. 配置输入输出映射 (可选)

### 3. 测试工作流
1. 在工作流列表中点击"测试"按钮
2. 填写测试输入参数
3. 运行测试查看结果

## 常见问题

### Q: 如何获取Dify中的App ID?
A: 在Dify控制台中，进入具体的App页面，在"设置"或"API"页面可以找到App ID。

### Q: 一个Dify服务可以配置多少个工作流?
A: 没有限制，可以配置该Dify服务中的所有App。

### Q: 可以连接多个Dify服务吗?
A: 可以，支持连接多个不同的Dify服务器或环境。

### Q: API密钥在哪里获取?
A: 在Dify控制台的"API管理"页面，每个App都有对应的API密钥。

## 最佳实践

1. **环境隔离**: 为不同环境 (开发、测试、生产) 配置不同的Dify服务
2. **命名规范**: 使用清晰的命名来区分不同的服务和工作流
3. **定期测试**: 使用测试功能验证工作流的正确性
4. **权限管理**: 确保API密钥的安全性，定期更换
5. **监控日志**: 关注执行记录，及时发现和解决问题
