# Dify内容生成工作流示例 - 支持APP级别配置
# 展示如何在工作流中配置专用的APP秘钥

workflow:
  name: "AI内容生成工作流 (内容生成器APP)"
  description: "使用Dify内容生成器APP生成视频标题、描述和标签"
  version: "2.0.0"
  author: "ThunderHub"
  
  # APP配置 - 新增字段
  app_id: "content_generator_app"
  app_name: "内容生成器"
  app_secret: "${DIFY_CONTENT_GENERATOR_SECRET}"  # 从环境变量获取APP秘钥
  
  # 工作流步骤
  steps:
    # 步骤1: 生成视频标题
    - id: "generate_title"
      name: "生成视频标题"
      description: "使用Dify内容生成器APP根据视频内容生成吸引人的标题"
      action: "dify_workflow"
      required: true
      timeout: 60
      parameters:
        instance_id: "default_dify_instance"  # Dify实例ID（提供base_url等基础配置）
        workflow_id: "title_generator"        # Dify工作流ID
        # 注意：APP秘钥将从工作流配置的app_secret字段获取，不再从instance配置获取
        inputs:
          video_topic: "${video_topic}"       # 从上下文获取视频主题
          target_audience: "${target_audience}" # 目标受众
          video_duration: "${video_duration}" # 视频时长
          keywords: "${keywords}"             # 关键词
        output_mappings:
          title: "generated_title"            # 将Dify输出的title映射到上下文的generated_title
          title_alternatives: "title_options" # 备选标题
      notes: "使用专用APP秘钥，生成的标题将保存到工作流上下文中"

    # 步骤2: 生成视频描述
    - id: "generate_description"
      name: "生成视频描述"
      description: "基于生成的标题创建详细的视频描述"
      action: "dify_workflow"
      required: true
      timeout: 90
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "description_generator"
        inputs:
          video_title: "${generated_title}"   # 使用上一步生成的标题
          video_topic: "${video_topic}"
          target_audience: "${target_audience}"
          video_duration: "${video_duration}"
          platform: "YouTube"
        output_mappings:
          description: "generated_description"
          hashtags: "generated_hashtags"
      notes: "基于标题生成SEO优化的描述"

    # 步骤3: 生成视频标签
    - id: "generate_tags"
      name: "生成视频标签"
      description: "为视频生成相关标签以提高搜索可见性"
      action: "dify_workflow"
      required: false  # 可选步骤
      timeout: 45
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "tags_generator"
        inputs:
          video_title: "${generated_title}"
          video_description: "${generated_description}"
          video_topic: "${video_topic}"
          platform: "YouTube"
        output_mappings:
          tags: "generated_tags"
      notes: "生成SEO友好的标签列表"

    # 步骤4: 内容质量检查
    - id: "content_quality_check"
      name: "内容质量检查"
      description: "检查生成内容的质量和合规性"
      action: "dify_workflow"
      required: false
      timeout: 30
      parameters:
        instance_id: "default_dify_instance"
        workflow_id: "content_quality_checker"
        inputs:
          title: "${generated_title}"
          description: "${generated_description}"
          tags: "${generated_tags}"
          platform_guidelines: "YouTube社区准则"
        output_mappings:
          quality_score: "content_quality_score"
          suggestions: "improvement_suggestions"
          compliance_check: "compliance_status"
      notes: "确保内容符合平台规范"

# 工作流配置
config:
  # APP配置说明
  app_config:
    description: "此工作流使用专用的Dify APP配置"
    app_priority: "工作流级别的app_secret优先于实例级别的api_key"
    fallback_strategy: "如果工作流未配置app_secret，将使用实例的api_key"
  
  # 默认参数
  default_parameters:
    target_audience: "18-35岁年轻人"
    platform: "YouTube"
    content_style: "吸引人且专业"
    
  # 重试配置
  retry_config:
    max_retries: 3
    retry_delay: 5
    
  # 超时配置
  timeout_config:
    default_step_timeout: 60
    max_workflow_timeout: 600
    
  # 错误处理
  error_handling:
    continue_on_optional_failure: true
    save_partial_results: true
    
  # 输出配置
  output_config:
    save_to_database: true
    export_format: "json"
    include_metadata: true

# 工作流元数据
metadata:
  tags:
    - "内容生成"
    - "Dify集成"
    - "APP级别配置"
    - "AI自动化"
  
  app_info:
    app_name: "内容生成器"
    app_description: "专用于生成视频内容的Dify应用"
    required_permissions: ["workflow:execute", "content:generate"]
  
  required_inputs:
    - video_topic: "视频主题"
    - target_audience: "目标受众"
    - video_duration: "视频时长（秒）"
    - keywords: "关键词列表"
  
  optional_inputs:
    - platform: "发布平台（默认YouTube）"
    - content_style: "内容风格"
  
  generated_outputs:
    - generated_title: "AI生成的视频标题"
    - generated_description: "AI生成的视频描述"
    - generated_tags: "AI生成的视频标签"
    - generated_hashtags: "AI生成的话题标签"
    - content_quality_score: "内容质量评分"

# 使用说明
usage:
  setup:
    - "在环境变量中设置 DIFY_CONTENT_GENERATOR_SECRET"
    - "确保Dify实例 'default_dify_instance' 已配置"
    - "验证工作流ID在Dify平台中存在"
  
  execution:
    - "提供必需的输入参数"
    - "工作流将自动使用配置的APP秘钥"
    - "生成的内容将保存到工作流上下文中"
  
  monitoring:
    - "检查执行日志了解详细信息"
    - "监控内容质量评分"
    - "根据建议优化输入参数"
