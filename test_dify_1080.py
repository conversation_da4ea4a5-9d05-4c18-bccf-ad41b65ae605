#!/usr/bin/env python3
"""
测试 Dify 1080 端口 API 的脚本
"""

import asyncio
import aiohttp
import json

async def test_dify_1080():
    """测试 Dify 1080 端口 API"""
    
    base_url = "http://***************:1080"
    
    # 测试不同的端点
    test_endpoints = [
        "/v1",                    # API v1 根路径
        "/v1/parameters",         # 参数端点（日志中看到的）
        "/v1/info",              # 信息端点
        "/v1/health",            # 健康检查
        "/console/api/apps",     # 控制台 API（日志中看到的）
        "/api/v1",               # 可能的 API 路径
    ]
    
    print("🧪 测试 Dify 1080 端口 API...")
    print("=" * 60)
    
    async with aiohttp.ClientSession(
        timeout=aiohttp.ClientTimeout(total=10),
        headers={"User-Agent": "ThunderHub-Dify-Client/1.0"}
    ) as session:
        
        for endpoint in test_endpoints:
            url = base_url + endpoint
            try:
                print(f"\n📡 测试: {url}")
                
                async with session.get(url) as response:
                    print(f"   状态码: {response.status}")
                    print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
                    
                    if response.status == 200:
                        try:
                            # 尝试解析 JSON
                            data = await response.json()
                            print(f"   ✅ JSON 响应:")
                            print(f"   {json.dumps(data, indent=4, ensure_ascii=False)[:500]}...")
                            
                            # 检查是否是 Dify API 响应
                            if isinstance(data, dict):
                                if 'version' in data or 'api_version' in data:
                                    print(f"   🎯 这可能是 Dify API 信息端点!")
                                elif 'data' in data:
                                    print(f"   🎯 这可能是 Dify API 数据端点!")
                                    
                        except json.JSONDecodeError:
                            # 不是 JSON，读取文本
                            text = await response.text()
                            if 'dify' in text.lower():
                                print(f"   🎯 包含 Dify 内容!")
                            elif 'html' in text.lower():
                                print(f"   📄 HTML 页面")
                            
                            preview = text[:200].replace('\n', ' ').strip()
                            print(f"   📝 内容预览: {preview}...")
                    
                    elif response.status == 404:
                        print(f"   ❌ 端点不存在")
                    elif response.status == 401:
                        print(f"   🔐 需要认证")
                    elif response.status == 403:
                        print(f"   🚫 访问被拒绝")
                    else:
                        print(f"   ⚠️ 其他状态码")
                        
            except asyncio.TimeoutError:
                print(f"   ⏰ 超时")
            except aiohttp.ClientConnectorError:
                print(f"   ❌ 连接失败")
            except Exception as e:
                print(f"   ❌ 错误: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📋 总结:")
    print("根据日志分析，您的 Dify API 应该在:")
    print("🎯 http://***************:1080/v1")
    print("")
    print("如果测试成功，请更新 ThunderHub 中的 Dify 实例配置")

if __name__ == "__main__":
    asyncio.run(test_dify_1080())
