#!/usr/bin/env python3
"""
测试 Dify API 的脚本
"""

import asyncio
import aiohttp
import json

async def test_dify_api():
    """测试 Dify API"""
    
    backend_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            print("🧪 测试 Dify 实例列表 API...")
            
            # 测试获取实例列表
            async with session.get(f"{backend_url}/api/v1/dify/instances") as response:
                print(f"状态码: {response.status}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"响应数据类型: {type(data)}")
                    print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    # 检查数据结构
                    if isinstance(data, dict):
                        print(f"✅ 响应是字典类型")
                        print(f"包含的键: {list(data.keys())}")
                        
                        if 'data' in data:
                            instances = data['data']
                            print(f"✅ 找到 data 字段，类型: {type(instances)}")
                            print(f"实例数量: {len(instances) if isinstance(instances, list) else 'N/A'}")
                            
                            if isinstance(instances, list) and len(instances) > 0:
                                print("✅ 实例列表:")
                                for i, instance in enumerate(instances, 1):
                                    print(f"  {i}. {instance.get('name', 'Unknown')}")
                                    print(f"     URL: {instance.get('base_url', 'Unknown')}")
                                    print(f"     ID: {instance.get('id', 'Unknown')}")
                            else:
                                print("⚠️ 实例列表为空")
                        else:
                            print("❌ 响应中没有 data 字段")
                    else:
                        print(f"❌ 响应不是字典类型: {type(data)}")
                else:
                    error_text = await response.text()
                    print(f"❌ API 请求失败: {error_text}")
                    
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_dify_api())
