#!/usr/bin/env python3
"""
检查 Dify 服务状态的脚本
"""

import asyncio
import aiohttp
import json

async def check_dify_service():
    """检查 Dify 服务状态"""
    
    # 常见的 Dify 服务端口和路径
    test_urls = [
        "http://192.168.123.103:5001/v1",      # 常见的 Dify API 端口
        "http://192.168.123.103:5001/api/v1",  # 带 api 前缀
        "http://192.168.123.103:5001",         # 根路径
        "http://192.168.123.103:8000/v1",      # 另一个常见端口
        "http://192.168.123.103:8000/api/v1",  # 带 api 前缀
        "http://192.168.123.103:8000",         # 根路径
        "http://192.168.123.103:3000/v1",      # 前端端口
        "http://192.168.123.103:3000/api/v1",  # 前端 API
        "http://192.168.123.103:3000",         # 前端根路径
        "http://192.168.123.103:1080/v1",      # 您日志中的端口
        "http://192.168.123.103:1080/api/v1",  # 1080端口带api前缀
        "http://192.168.123.103:1080",         # 1080端口根路径
        "http://192.168.123.103/v1",           # 您配置的地址
        "http://192.168.123.103/api/v1",       # 带 api 前缀
        "http://192.168.123.103:80/v1",        # 明确指定 80 端口
        "http://192.168.123.103:80/api/v1",    # 80 端口带 api 前缀
    ]
    
    print("🔍 检查 Dify 服务状态...")
    print("=" * 60)
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
        for url in test_urls:
            try:
                print(f"\n📡 测试: {url}")
                
                # 测试根路径
                async with session.get(url) as response:
                    print(f"   状态码: {response.status}")
                    print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
                    
                    # 读取响应内容的前200个字符
                    text = await response.text()
                    preview = text[:200].replace('\n', ' ').strip()
                    print(f"   响应预览: {preview}...")
                    
                    # 检查是否是 JSON 响应
                    if 'application/json' in response.headers.get('content-type', ''):
                        try:
                            data = json.loads(text)
                            print(f"   ✅ JSON 响应: {data}")
                        except:
                            print(f"   ❌ JSON 解析失败")
                    
                    # 检查是否是 Dify API
                    if 'dify' in text.lower() or 'api' in text.lower():
                        print(f"   🎯 可能是 Dify API!")
                    elif 'gitlab' in text.lower():
                        print(f"   ⚠️ 这是 GitLab 服务")
                    elif 'html' in text.lower():
                        print(f"   ⚠️ 这是 HTML 页面，不是 API")
                        
            except asyncio.TimeoutError:
                print(f"   ⏰ 超时")
            except aiohttp.ClientConnectorError:
                print(f"   ❌ 连接失败")
            except Exception as e:
                print(f"   ❌ 错误: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🔧 建议:")
    print("1. 检查 Dify 服务是否正在运行")
    print("2. 确认 Dify API 服务的正确端口")
    print("3. 查看 Dify 的配置文件或启动日志")
    print("4. 尝试访问 Dify 的管理界面确认服务状态")

if __name__ == "__main__":
    asyncio.run(check_dify_service())
