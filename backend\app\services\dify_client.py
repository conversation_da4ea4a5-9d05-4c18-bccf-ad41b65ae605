"""
Dify API客户端
提供与Dify平台交互的HTTP客户端功能
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from urllib.parse import urljoin

import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from app.core.schemas.dify_models import (
    DifyInstanceConfig,
    DifyWorkflowConfig,
    DifyExecutionRequest,
    DifyExecutionResponse,
    DifyExecutionStatus,
    DifyTestResponse
)

logger = logging.getLogger(__name__)


class DifyAPIError(Exception):
    """Dify API错误"""
    def __init__(self, message: str, status_code: Optional[int] = None, error_code: Optional[str] = None):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        super().__init__(message)


class DifyClient:
    """Dify API客户端"""
    
    def __init__(self, instance_config: DifyInstanceConfig):
        self.config = instance_config
        self.session: Optional[aiohttp.ClientSession] = None

        # 构建请求头，处理可选的API密钥
        self._base_headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ThunderHub-Dify-Client/1.0'
        }

        # 只有当API密钥存在时才添加Authorization头
        if instance_config.api_key:
            self._base_headers['Authorization'] = f'Bearer {instance_config.api_key}'
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def connect(self):
        """建立连接"""
        if self.session is None:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=self._base_headers
            )
            
            logger.info(f"Dify客户端连接建立: {self.config.name}")
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info(f"Dify客户端连接关闭: {self.config.name}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError))
    )
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """发送HTTP请求"""
        if not self.session:
            await self.connect()
        
        url = urljoin(self.config.base_url, endpoint)
        request_timeout = timeout or self.config.timeout
        
        try:
            logger.debug(f"发送Dify API请求: {method} {url}")
            
            async with self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                timeout=aiohttp.ClientTimeout(total=request_timeout)
            ) as response:
                response_text = await response.text()
                
                if response.status >= 400:
                    logger.error(f"Dify API请求失败: {response.status} - {response_text}")
                    raise DifyAPIError(
                        message=f"API请求失败: {response_text}",
                        status_code=response.status
                    )
                
                try:
                    result = json.loads(response_text)
                    logger.debug(f"Dify API响应成功: {response.status}")
                    return result
                except json.JSONDecodeError:
                    logger.error(f"Dify API响应解析失败: {response_text}")
                    raise DifyAPIError(f"响应解析失败: {response_text}")
                    
        except asyncio.TimeoutError:
            logger.error(f"Dify API请求超时: {url}")
            raise DifyAPIError(f"请求超时: {url}")
        except aiohttp.ClientError as e:
            logger.error(f"Dify API连接错误: {str(e)}")
            raise DifyAPIError(f"连接错误: {str(e)}")
    
    async def test_connection(self) -> DifyTestResponse:
        """测试连接"""
        start_time = time.time()

        try:
            # 如果有API密钥，尝试获取应用信息来测试连接
            if self.config.api_key:
                result = await self._make_request('GET', '/v1/parameters', timeout=10)
                response_time = time.time() - start_time

                return DifyTestResponse(
                    success=True,
                    message="连接测试成功，API密钥有效",
                    response_time=response_time,
                    api_version=result.get('version', 'unknown'),
                    tested_at=datetime.now()
                )
            else:
                # 没有API密钥时，只测试基础连接
                timeout = aiohttp.ClientTimeout(total=10)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 尝试访问根路径或健康检查端点
                    test_url = f"{self.config.base_url.rstrip('/')}/health"
                    async with session.get(test_url) as response:
                        response_time = time.time() - start_time

                        return DifyTestResponse(
                            success=True,
                            message="服务器连接测试成功（未配置API密钥，仅测试网络连通性）",
                            response_time=response_time,
                            tested_at=datetime.now()
                        )

        except Exception as e:
            response_time = time.time() - start_time
            error_message = str(e)

            return DifyTestResponse(
                success=False,
                message="连接测试失败",
                response_time=response_time,
                error_details=error_message,
                tested_at=datetime.now()
            )
    
    async def execute_workflow(
        self,
        workflow_config: DifyWorkflowConfig,
        request: DifyExecutionRequest
    ) -> DifyExecutionResponse:
        """执行工作流"""
        start_time = datetime.now()
        
        try:
            # 应用输入参数映射
            mapped_inputs = self._apply_input_mappings(
                request.inputs,
                workflow_config.input_mappings
            )
            
            # 构建请求数据
            request_data = {
                'inputs': mapped_inputs,
                'response_mode': request.response_mode,
                'user': request.user_id or 'default_user'
            }
            
            if request.conversation_id:
                request_data['conversation_id'] = request.conversation_id
            
            # 根据工作流类型选择不同的端点
            if workflow_config.workflow_type == 'chatbot':
                endpoint = f'/v1/chat-messages'
            elif workflow_config.workflow_type == 'completion':
                endpoint = f'/v1/completion-messages'
            elif workflow_config.workflow_type == 'workflow':
                endpoint = f'/v1/workflows/run'
                request_data['workflow_id'] = workflow_config.workflow_id
            else:
                endpoint = f'/v1/workflows/run'
                request_data['workflow_id'] = workflow_config.workflow_id
            
            logger.info(f"执行Dify工作流: {workflow_config.name} ({workflow_config.workflow_id})")
            
            # 发送请求
            result = await self._make_request(
                'POST',
                endpoint,
                data=request_data,
                timeout=workflow_config.timeout
            )
            
            # 应用输出参数映射
            mapped_outputs = self._apply_output_mappings(
                result.get('data', {}),
                workflow_config.output_mappings
            )
            
            completed_at = datetime.now()
            duration = (completed_at - start_time).total_seconds()
            
            return DifyExecutionResponse(
                success=True,
                execution_id=result.get('id', ''),
                status=DifyExecutionStatus.COMPLETED,
                outputs=mapped_outputs,
                started_at=start_time,
                completed_at=completed_at,
                duration=duration,
                token_usage=result.get('metadata', {}).get('usage', {}),
                metadata=result.get('metadata', {})
            )
            
        except Exception as e:
            completed_at = datetime.now()
            duration = (completed_at - start_time).total_seconds()
            
            logger.error(f"Dify工作流执行失败: {str(e)}")
            
            return DifyExecutionResponse(
                success=False,
                status=DifyExecutionStatus.FAILED,
                error_message=str(e),
                started_at=start_time,
                completed_at=completed_at,
                duration=duration
            )
    
    async def execute_workflow_streaming(
        self,
        workflow_config: DifyWorkflowConfig,
        request: DifyExecutionRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """流式执行工作流"""
        if not self.session:
            await self.connect()
        
        # 应用输入参数映射
        mapped_inputs = self._apply_input_mappings(
            request.inputs,
            workflow_config.input_mappings
        )
        
        # 构建请求数据
        request_data = {
            'inputs': mapped_inputs,
            'response_mode': 'streaming',
            'user': request.user_id or 'default_user'
        }
        
        if request.conversation_id:
            request_data['conversation_id'] = request.conversation_id
        
        # 选择端点
        if workflow_config.workflow_type == 'chatbot':
            endpoint = f'/v1/chat-messages'
        elif workflow_config.workflow_type == 'completion':
            endpoint = f'/v1/completion-messages'
        else:
            endpoint = f'/v1/workflows/run'
            request_data['workflow_id'] = workflow_config.workflow_id
        
        url = urljoin(self.config.base_url, endpoint)
        
        try:
            async with self.session.post(url, json=request_data) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    raise DifyAPIError(f"流式请求失败: {error_text}", response.status)
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            yield data
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Dify流式执行失败: {str(e)}")
            raise DifyAPIError(f"流式执行失败: {str(e)}")
    
    def _apply_input_mappings(
        self,
        inputs: Dict[str, Any],
        mappings: List[Any]
    ) -> Dict[str, Any]:
        """应用输入参数映射"""
        if not mappings:
            return inputs
        
        mapped_inputs = {}
        
        for mapping in mappings:
            source_key = mapping.source_key
            target_key = mapping.target_key
            default_value = mapping.default_value
            
            if source_key in inputs:
                mapped_inputs[target_key] = inputs[source_key]
            elif default_value is not None:
                mapped_inputs[target_key] = default_value
            elif mapping.required:
                logger.warning(f"缺少必需的输入参数: {source_key}")
        
        return mapped_inputs
    
    def _apply_output_mappings(
        self,
        outputs: Dict[str, Any],
        mappings: List[Any]
    ) -> Dict[str, Any]:
        """应用输出参数映射"""
        if not mappings:
            return outputs
        
        mapped_outputs = {}
        
        for mapping in mappings:
            source_key = mapping.source_key
            target_key = mapping.target_key
            
            if source_key in outputs:
                mapped_outputs[target_key] = outputs[source_key]
        
        return mapped_outputs if mapped_outputs else outputs
