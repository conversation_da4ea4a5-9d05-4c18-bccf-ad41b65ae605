<template>
  <el-dialog
    v-model="visible"
    title="测试工作流"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="workflow-test">
      <!-- 工作流信息 -->
      <div class="workflow-info">
        <h4>工作流信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="工作流名称">
            {{ workflow?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工作流ID">
            {{ workflow?.workflow_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工作流类型">
            <el-tag :type="getWorkflowTypeColor(workflow?.workflow_type)">
              {{ getWorkflowTypeText(workflow?.workflow_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述">
            {{ workflow?.description || '无描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 测试输入 -->
      <div class="test-inputs">
        <h4>测试输入参数</h4>
        <el-form
          ref="formRef"
          :model="testForm"
          label-width="120px"
          class="test-form"
        >
          <!-- 动态输入参数 -->
          <div v-if="workflow?.input_mappings && workflow.input_mappings.length > 0">
            <el-form-item
              v-for="mapping in workflow.input_mappings"
              :key="mapping.source_key"
              :label="mapping.source_key"
              :prop="`inputs.${mapping.source_key}`"
            >
              <el-input
                v-model="testForm.inputs[mapping.source_key]"
                :placeholder="mapping.description || `请输入${mapping.source_key}`"
                type="textarea"
                :rows="2"
              />
              <div class="input-tip" v-if="mapping.description">
                {{ mapping.description }}
              </div>
            </el-form-item>
          </div>

          <!-- 通用输入参数 -->
          <div v-else>
            <el-form-item label="主题/内容" prop="inputs.topic">
              <el-input
                v-model="testForm.inputs.topic"
                placeholder="请输入测试主题或内容"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            
            <el-form-item label="目标受众" prop="inputs.target_audience">
              <el-input
                v-model="testForm.inputs.target_audience"
                placeholder="请输入目标受众（可选）"
              />
            </el-form-item>

            <el-form-item label="其他参数" prop="inputs.additional">
              <el-input
                v-model="testForm.inputs.additional"
                placeholder="请输入其他参数（JSON格式，可选）"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <h4>测试结果</h4>
        <el-alert
          :type="testResult.success ? 'success' : 'error'"
          :title="testResult.success ? '测试成功' : '测试失败'"
          :description="testResult.message"
          show-icon
          :closable="false"
        />
        
        <!-- 执行结果详情 -->
        <div v-if="testResult.success && testResult.execution_result" class="execution-details">
          <el-tabs v-model="resultTab" class="result-tabs">
            <el-tab-pane label="输出结果" name="outputs">
              <el-card>
                <pre class="result-content">{{ formatJson(testResult.execution_result.outputs) }}</pre>
              </el-card>
            </el-tab-pane>
            
            <el-tab-pane label="执行信息" name="execution">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="执行ID">
                  {{ testResult.execution_result.execution_id || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="响应时间">
                  {{ testResult.execution_result.response_time || '-' }}ms
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="testResult.execution_result.success ? 'success' : 'danger'">
                    {{ testResult.execution_result.success ? '成功' : '失败' }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-tab-pane>

            <el-tab-pane label="原始响应" name="raw">
              <el-card>
                <pre class="result-content">{{ formatJson(testResult.execution_result) }}</pre>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          @click="runTest"
          :loading="testing"
          :disabled="!canTest"
        >
          {{ testing ? '测试中...' : '运行测试' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import difyApi, { type DifyWorkflow } from '@/api/dify'

// Props
interface Props {
  modelValue: boolean
  workflow?: DifyWorkflow | null
}

const props = withDefaults(defineProps<Props>(), {
  workflow: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const testing = ref(false)
const testResult = ref<any>(null)
const resultTab = ref('outputs')

// 测试表单数据
const testForm = reactive({
  inputs: {} as Record<string, any>
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const canTest = computed(() => {
  return props.workflow?.id && Object.keys(testForm.inputs).length > 0
})

// 方法
const resetForm = () => {
  testForm.inputs = {}
  testResult.value = null
  resultTab.value = 'outputs'
  formRef.value?.clearValidate()
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const runTest = async () => {
  if (!props.workflow?.id) {
    ElMessage.error('工作流信息不完整')
    return
  }

  testing.value = true
  testResult.value = null

  try {
    // 处理额外参数
    let finalInputs = { ...testForm.inputs }
    
    if (finalInputs.additional) {
      try {
        const additionalParams = JSON.parse(finalInputs.additional)
        finalInputs = { ...finalInputs, ...additionalParams }
        delete finalInputs.additional
      } catch (e) {
        ElMessage.warning('额外参数JSON格式错误，将忽略')
        delete finalInputs.additional
      }
    }

    const response = await difyApi.workflow.test(props.workflow.id, finalInputs)
    testResult.value = response
    
    if (response.success) {
      ElMessage.success('工作流测试成功')
    } else {
      ElMessage.error(`工作流测试失败: ${response.message}`)
    }
  } catch (error: any) {
    console.error('工作流测试失败:', error)
    testResult.value = {
      success: false,
      message: error.message || '测试请求失败',
      error_details: error
    }
    ElMessage.error('工作流测试失败')
  } finally {
    testing.value = false
  }
}

const formatJson = (obj: any) => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch {
    return String(obj)
  }
}

const getWorkflowTypeColor = (type?: string) => {
  const colors: Record<string, string> = {
    'chatbot': 'primary',
    'workflow': 'success',
    'agent': 'warning',
    'completion': 'info'
  }
  return colors[type || ''] || 'info'
}

const getWorkflowTypeText = (type?: string) => {
  const texts: Record<string, string> = {
    'chatbot': '聊天机器人',
    'workflow': '工作流',
    'agent': '智能体',
    'completion': '文本补全'
  }
  return texts[type || ''] || type || '未知'
}

// 监听工作流变化，初始化测试参数
watch(() => props.workflow, (newWorkflow) => {
  if (newWorkflow) {
    resetForm()
    
    // 根据输入映射初始化测试参数
    if (newWorkflow.input_mappings && newWorkflow.input_mappings.length > 0) {
      newWorkflow.input_mappings.forEach(mapping => {
        testForm.inputs[mapping.source_key] = mapping.default_value || ''
      })
    } else {
      // 默认参数
      testForm.inputs = {
        topic: '',
        target_audience: '',
        additional: ''
      }
    }
  }
}, { immediate: true })
</script>

<style scoped>
.workflow-test {
  max-height: 70vh;
  overflow-y: auto;
}

.workflow-info,
.test-inputs,
.test-result {
  margin-bottom: 24px;
}

.workflow-info h4,
.test-inputs h4,
.test-result h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.test-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.input-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.execution-details {
  margin-top: 16px;
}

.result-tabs {
  margin-top: 16px;
}

.result-content {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
