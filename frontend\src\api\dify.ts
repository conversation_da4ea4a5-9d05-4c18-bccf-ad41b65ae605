import request from '@/utils/request'

// Dify实例相关接口
export interface DifyInstance {
  id?: string
  name: string
  base_url: string
  api_key: string
  timeout?: number
  max_retries?: number
  retry_delay?: number
  status?: 'active' | 'inactive' | 'error'
  last_test_at?: string
  last_error?: string
  description?: string
  tags?: string[]
  enabled?: boolean
  created_at?: string
  updated_at?: string
}

// Dify工作流配置
export interface DifyWorkflow {
  id?: string
  name: string
  workflow_id: string
  workflow_type: 'chatbot' | 'workflow' | 'agent' | 'completion'
  description?: string
  instance_id?: string
  input_mappings?: DifyParameterMapping[]
  output_mappings?: DifyParameterMapping[]
  timeout?: number
  max_retries?: number
  retry_delay?: number
  tags?: string[]
  enabled?: boolean
  created_at?: string
  updated_at?: string
}

// 参数映射
export interface DifyParameterMapping {
  source_key: string
  target_key: string
  data_type?: string
  default_value?: any
  required?: boolean
  description?: string
}

// 执行请求
export interface DifyExecutionRequest {
  instance_id: string
  workflow_id: string
  inputs: Record<string, any>
  user_id?: string
  conversation_id?: string
  response_mode?: 'blocking' | 'streaming'
  task_id?: string
  metadata?: Record<string, any>
}

// 执行响应
export interface DifyExecutionResponse {
  success: boolean
  execution_id?: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  outputs?: Record<string, any>
  error_message?: string
  started_at?: string
  completed_at?: string
  duration?: number
  token_usage?: Record<string, number>
  cost?: number
  metadata?: Record<string, any>
}

// 执行日志
export interface DifyExecutionLog {
  id?: string
  execution_id: string
  task_id?: string
  instance_id: string
  workflow_id: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  request_data?: Record<string, any>
  response_data?: Record<string, any>
  started_at: string
  completed_at?: string
  duration?: number
  error_message?: string
  error_code?: string
  retry_count?: number
  token_usage?: Record<string, number>
  cost?: number
  metadata?: Record<string, any>
  created_at?: string
}

// 测试请求
export interface DifyTestRequest {
  instance_id?: string
  base_url?: string
  api_key?: string
  timeout?: number
}

// 测试响应
export interface DifyTestResponse {
  success: boolean
  message: string
  response_time?: number
  api_version?: string
  error_details?: string
  tested_at: string
}

// API响应包装
export interface ApiResponse<T> {
  success: boolean
  data: T
  total?: number
  message: string
}

// Dify实例管理API
export const difyInstanceApi = {
  // 创建实例
  create: (data: DifyInstance) => 
    request.post<ApiResponse<{ instance_id: string }>>('/api/v1/dify/instances', data),

  // 获取实例列表
  list: (params?: { enabled_only?: boolean }) => 
    request.get<ApiResponse<DifyInstance[]>>('/api/v1/dify/instances', { params }),

  // 获取实例详情
  get: (instanceId: string) => 
    request.get<ApiResponse<DifyInstance>>(`/api/v1/dify/instances/${instanceId}`),

  // 更新实例
  update: (instanceId: string, data: DifyInstance) => 
    request.put<ApiResponse<any>>(`/api/v1/dify/instances/${instanceId}`, data),

  // 删除实例
  delete: (instanceId: string) => 
    request.delete<ApiResponse<any>>(`/api/v1/dify/instances/${instanceId}`),

  // 测试连接
  test: (data: DifyTestRequest) => 
    request.post<DifyTestResponse>('/api/v1/dify/instances/test', data)
}

// Dify工作流管理API
export const difyWorkflowApi = {
  // 创建工作流配置
  create: (instanceId: string, data: DifyWorkflow) => 
    request.post<ApiResponse<{ workflow_id: string }>>(`/api/v1/dify/instances/${instanceId}/workflows`, data),

  // 获取工作流列表
  list: (params?: { instance_id?: string; enabled_only?: boolean }) => 
    request.get<ApiResponse<DifyWorkflow[]>>('/api/v1/dify/workflows', { params }),

  // 获取工作流详情
  get: (workflowId: string) => 
    request.get<ApiResponse<DifyWorkflow>>(`/api/v1/dify/workflows/${workflowId}`),

  // 更新工作流配置
  update: (workflowId: string, data: DifyWorkflow) => 
    request.put<ApiResponse<any>>(`/api/v1/dify/workflows/${workflowId}`, data),

  // 删除工作流配置
  delete: (workflowId: string) =>
    request.delete<ApiResponse<any>>(`/api/v1/dify/workflows/${workflowId}`),

  // 测试工作流
  test: (workflowId: string, testInputs: Record<string, any>) =>
    request.post<ApiResponse<any>>(`/api/v1/dify/workflows/${workflowId}/test`, testInputs)
}

// Dify工作流执行API
export const difyExecutionApi = {
  // 执行工作流
  execute: (data: DifyExecutionRequest) => 
    request.post<DifyExecutionResponse>('/api/v1/dify/execute', data),

  // 获取执行记录列表
  list: (params?: { 
    instance_id?: string
    workflow_id?: string
    task_id?: string
    limit?: number 
  }) => 
    request.get<ApiResponse<DifyExecutionLog[]>>('/api/v1/dify/executions', { params }),

  // 获取执行记录详情
  get: (executionId: string) => 
    request.get<ApiResponse<DifyExecutionLog>>(`/api/v1/dify/executions/${executionId}`)
}

// 导出所有API
export default {
  instance: difyInstanceApi,
  workflow: difyWorkflowApi,
  execution: difyExecutionApi
}
