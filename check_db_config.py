#!/usr/bin/env python3
"""
检查数据库中的 Dify 配置
"""

import asyncio
from motor.motor_asyncio import AsyncIOMotorClient
import json

async def check_db_config():
    """检查数据库中的 Dify 配置"""
    
    try:
        # 连接 MongoDB
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.thunderhub
        collection = db.dify_instances
        
        print("🔍 检查数据库中的 Dify 实例配置...")
        print("=" * 60)
        
        # 查找所有实例
        cursor = collection.find({})
        instances = []
        async for doc in cursor:
            instances.append(doc)
        
        if not instances:
            print("❌ 数据库中没有找到 Dify 实例")
        else:
            print(f"📋 找到 {len(instances)} 个 Dify 实例:")
            for i, instance in enumerate(instances, 1):
                print(f"\n{i}. 实例信息:")
                print(f"   ID: {instance.get('_id', 'Unknown')}")
                print(f"   名称: {instance.get('name', 'Unknown')}")
                print(f"   API地址: {instance.get('base_url', 'Unknown')}")
                print(f"   状态: {instance.get('status', 'Unknown')}")
                print(f"   创建时间: {instance.get('created_at', 'Unknown')}")
                print(f"   最后错误: {instance.get('last_error', 'None')}")
                
                # 检查是否需要更新
                current_url = instance.get('base_url', '')
                if ':5001' in current_url:
                    print(f"   ⚠️ 需要更新：当前使用错误的端口 5001")
                    correct_url = current_url.replace(':5001', ':1080')
                    print(f"   ✅ 建议更新为: {correct_url}")
        
        await client.close()
        
    except Exception as e:
        print(f"❌ 检查数据库配置失败: {str(e)}")

async def update_db_config():
    """更新数据库中的 Dify 配置"""
    
    try:
        # 连接 MongoDB
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.thunderhub
        collection = db.dify_instances
        
        print("\n🔧 更新数据库中的 Dify 实例配置...")
        print("=" * 60)
        
        # 查找所有使用 5001 端口的实例
        cursor = collection.find({"base_url": {"$regex": ":5001"}})
        instances_to_update = []
        async for doc in cursor:
            instances_to_update.append(doc)
        
        if not instances_to_update:
            print("✅ 没有需要更新的实例")
        else:
            print(f"📋 找到 {len(instances_to_update)} 个需要更新的实例:")
            
            for instance in instances_to_update:
                old_url = instance.get('base_url', '')
                new_url = old_url.replace(':5001', ':1080')
                instance_id = instance['_id']
                
                print(f"\n更新实例: {instance.get('name', 'Unknown')}")
                print(f"  旧地址: {old_url}")
                print(f"  新地址: {new_url}")
                
                # 更新实例
                result = await collection.update_one(
                    {'_id': instance_id},
                    {
                        '$set': {
                            'base_url': new_url,
                            'status': 'inactive',  # 重置状态
                            'last_error': None
                        }
                    }
                )
                
                if result.modified_count > 0:
                    print(f"  ✅ 更新成功")
                else:
                    print(f"  ❌ 更新失败")
        
        await client.close()
        print("\n🎉 数据库配置更新完成！")
        print("请重启 ThunderHub 后端服务以应用更改")
        
    except Exception as e:
        print(f"❌ 更新数据库配置失败: {str(e)}")

async def main():
    """主函数"""
    await check_db_config()
    
    # 询问是否要更新
    print("\n" + "=" * 60)
    response = input("是否要自动更新配置？(y/n): ").strip().lower()
    
    if response in ['y', 'yes', '是']:
        await update_db_config()
    else:
        print("跳过更新。您可以手动在前端界面中更新配置。")

if __name__ == "__main__":
    asyncio.run(main())
