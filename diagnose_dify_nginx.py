#!/usr/bin/env python3
"""
诊断 Dify Nginx 配置的脚本
"""

import asyncio
import aiohttp
import json

async def diagnose_dify_nginx():
    """诊断 Dify Nginx 配置"""
    
    print("🔍 诊断 Dify Nginx 配置...")
    print("=" * 60)
    
    # 测试不同的路径
    test_paths = [
        "",           # 根路径
        "/",          # 根路径
        "/v1",        # API v1
        "/api/v1",    # API v1 带前缀
        "/docs",      # API 文档
        "/health",    # 健康检查
        "/console",   # Dify 控制台
        "/api",       # API 根路径
    ]
    
    base_url = "http://192.168.123.103:1080"
    
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
        for path in test_paths:
            url = base_url + path
            try:
                print(f"\n📡 测试: {url}")
                
                async with session.get(url) as response:
                    print(f"   状态码: {response.status}")
                    print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
                    
                    # 读取响应内容
                    text = await response.text()
                    
                    # 检查响应类型
                    if 'application/json' in response.headers.get('content-type', ''):
                        try:
                            data = json.loads(text)
                            print(f"   ✅ JSON 响应: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                        except:
                            print(f"   ❌ JSON 解析失败")
                    elif 'text/html' in response.headers.get('content-type', ''):
                        # 检查 HTML 内容
                        if 'dify' in text.lower():
                            print(f"   🎯 包含 Dify 内容!")
                        elif 'gitlab' in text.lower():
                            print(f"   ⚠️ 这是 GitLab 页面")
                        elif '<title>' in text.lower():
                            # 提取标题
                            import re
                            title_match = re.search(r'<title[^>]*>([^<]+)</title>', text, re.IGNORECASE)
                            if title_match:
                                print(f"   📄 页面标题: {title_match.group(1)}")
                        
                        # 显示前200个字符
                        preview = text[:200].replace('\n', ' ').strip()
                        print(f"   📝 内容预览: {preview}...")
                    else:
                        # 其他类型的响应
                        preview = text[:200].replace('\n', ' ').strip()
                        print(f"   📄 响应预览: {preview}...")
                        
            except asyncio.TimeoutError:
                print(f"   ⏰ 超时")
            except aiohttp.ClientConnectorError:
                print(f"   ❌ 连接失败")
            except Exception as e:
                print(f"   ❌ 错误: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🔧 建议:")
    print("1. 检查 Nginx 配置文件")
    print("2. 确认 Dify API 服务是否正常运行")
    print("3. 检查 Docker 网络配置")
    print("4. 查看 Nginx 和 Dify 容器日志")

if __name__ == "__main__":
    asyncio.run(diagnose_dify_nginx())
