#!/usr/bin/env python3
"""
配置 Dify 实例的脚本
"""

import asyncio
import aiohttp
import json

async def configure_dify_instance():
    """配置 Dify 实例"""
    
    # Dify 实例配置
    dify_config = {
        "name": "我的Dify服务",
        "base_url": "http://***************/v1",
        "api_key": "YOUR_DIFY_API_KEY",  # 请替换为您的实际 API Key
        "description": "本地Dify服务实例",
        "timeout": 30,
        "max_retries": 3,
        "retry_delay": 2,
        "enabled": True,
        "tags": ["本地", "内容生成"]
    }
    
    # ThunderHub 后端 API 地址
    backend_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 1. 创建 Dify 实例
            print("正在创建 Dify 实例...")
            async with session.post(
                f"{backend_url}/api/v1/dify/instances",
                json=dify_config,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    instance_id = result["data"]["instance_id"]
                    print(f"✅ Dify 实例创建成功！实例ID: {instance_id}")
                else:
                    error_text = await response.text()
                    print(f"❌ 创建失败: {response.status} - {error_text}")
                    return
            
            # 2. 测试连接
            print("正在测试连接...")
            test_config = {
                "instance_id": instance_id
            }
            
            async with session.post(
                f"{backend_url}/api/v1/dify/instances/test",
                json=test_config,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    test_result = await response.json()
                    if test_result.get("success"):
                        print(f"✅ 连接测试成功！响应时间: {test_result.get('response_time', 'N/A')}秒")
                    else:
                        print(f"❌ 连接测试失败: {test_result.get('message')}")
                else:
                    error_text = await response.text()
                    print(f"❌ 测试请求失败: {response.status} - {error_text}")
            
            # 3. 获取实例列表验证
            print("正在验证实例列表...")
            async with session.get(f"{backend_url}/api/v1/dify/instances") as response:
                if response.status == 200:
                    instances = await response.json()
                    print(f"✅ 当前共有 {len(instances.get('data', []))} 个 Dify 实例")
                    for instance in instances.get('data', []):
                        print(f"  - {instance['name']}: {instance['base_url']}")
                else:
                    print(f"❌ 获取实例列表失败: {response.status}")
                    
        except Exception as e:
            print(f"❌ 配置过程中出现错误: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始配置 Dify 实例...")
    print("📝 请确保：")
    print("   1. ThunderHub 后端服务正在运行 (http://localhost:8000)")
    print("   2. 您的 Dify 服务正在运行 (http://***************/v1)")
    print("   3. 已将脚本中的 YOUR_DIFY_API_KEY 替换为实际的 API Key")
    print()
    
    asyncio.run(configure_dify_instance())
