<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑Dify实例' : '添加Dify实例'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="instance-form"
    >
      <el-form-item label="Dify服务名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入Dify服务名称，如：生产环境、测试环境"
          maxlength="50"
          show-word-limit
        />
        <div class="form-tip">
          为这个Dify服务器起一个便于识别的名称
        </div>
      </el-form-item>

      <el-form-item label="API地址" prop="base_url">
        <el-input
          v-model="form.base_url"
          placeholder="https://api.dify.ai"
          maxlength="200"
        />
        <div class="form-tip">
          Dify服务器的API地址，如：https://api.dify.ai 或 http://your-dify-server.com/v1
        </div>
      </el-form-item>



      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="请输入实例描述（可选）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="添加标签（可选）"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
        <div class="form-tip">
          用于分类和筛选实例
        </div>
      </el-form-item>

      <el-divider content-position="left">高级配置</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="超时时间" prop="timeout">
            <el-input-number
              v-model="form.timeout"
              :min="1"
              :max="300"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">秒</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最大重试" prop="max_retries">
            <el-input-number
              v-model="form.max_retries"
              :min="0"
              :max="10"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">次</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重试延迟" prop="retry_delay">
            <el-input-number
              v-model="form.retry_delay"
              :min="0.1"
              :max="60"
              :step="0.1"
              :precision="1"
              style="width: 100%"
            />
            <div class="form-tip">秒</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-checkbox v-model="form.enabled">启用此实例</el-checkbox>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleTest"
          :loading="testing"
          :disabled="!canTest"
        >
          测试连接
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSave"
          :loading="saving"
        >
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import difyApi, { type DifyInstance } from '@/api/dify'

// Props
interface Props {
  modelValue: boolean
  instance?: DifyInstance | null
}

const props = withDefaults(defineProps<Props>(), {
  instance: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'saved': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const saving = ref(false)
const testing = ref(false)

// 表单数据
const form = reactive<DifyInstance>({
  name: '',
  base_url: '',
  api_key: '', // 保留字段但不在界面显示，用于兼容性
  description: '',
  tags: [],
  timeout: 30,
  max_retries: 3,
  retry_delay: 1.0,
  enabled: true
})

// 常用标签
const commonTags = [
  '生产环境',
  '测试环境',
  '开发环境',
  '文本生成',
  '图像处理',
  '数据分析',
  '客服机器人',
  '内容创作'
]

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入实例名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  base_url: [
    { required: true, message: '请输入API地址', trigger: 'blur' },
    { 
      pattern: /^https?:\/\/.+/, 
      message: '请输入有效的URL地址', 
      trigger: 'blur' 
    }
  ],
  // api_key 验证已移除，现在在工作流级别配置
  timeout: [
    { required: true, message: '请输入超时时间', trigger: 'blur' },
    { type: 'number', min: 1, max: 300, message: '超时时间在1-300秒之间', trigger: 'blur' }
  ],
  max_retries: [
    { required: true, message: '请输入最大重试次数', trigger: 'blur' },
    { type: 'number', min: 0, max: 10, message: '重试次数在0-10次之间', trigger: 'blur' }
  ],
  retry_delay: [
    { required: true, message: '请输入重试延迟', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 60, message: '重试延迟在0.1-60秒之间', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.instance?.id)

const canTest = computed(() => {
  return form.base_url && !testing.value
})

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    base_url: '',
    api_key: '',
    description: '',
    tags: [],
    timeout: 30,
    max_retries: 3,
    retry_delay: 1.0,
    enabled: true
  })
  formRef.value?.clearValidate()
}

// 监听实例变化
watch(() => props.instance, (newInstance) => {
  if (newInstance) {
    Object.assign(form, {
      ...newInstance,
      tags: newInstance.tags || []
    })
  } else {
    resetForm()
  }
}, { immediate: true })

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleTest = async () => {
  // 先验证必填字段
  try {
    await formRef.value?.validateField(['base_url', 'api_key'])
  } catch {
    return
  }

  testing.value = true
  try {
    const response = await difyApi.instance.test({
      base_url: form.base_url,
      api_key: form.api_key,
      timeout: form.timeout
    })
    
    if (response.data.success) {
      ElMessage.success(`连接测试成功！响应时间: ${response.data.response_time}ms`)
    } else {
      ElMessage.error(`连接测试失败: ${response.data.message}`)
    }
  } catch (error) {
    ElMessage.error('连接测试失败，请检查网络和配置')
    console.error(error)
  } finally {
    testing.value = false
  }
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
  } catch {
    return
  }

  saving.value = true
  try {
    if (isEdit.value) {
      await difyApi.instance.update(props.instance!.id!, form)
      ElMessage.success('实例更新成功')
    } else {
      await difyApi.instance.create(form)
      ElMessage.success('实例创建成功')
    }
    
    emit('saved')
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error(error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.instance-form {
  padding: 0 20px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}
</style>
