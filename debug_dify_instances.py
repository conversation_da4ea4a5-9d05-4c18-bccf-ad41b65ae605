#!/usr/bin/env python3
"""
调试 Dify 实例数据的脚本
"""

import asyncio
import aiohttp
import json
from motor.motor_asyncio import AsyncIOMotorClient

async def debug_dify_instances():
    """调试 Dify 实例数据"""
    
    print("🔍 开始调试 Dify 实例数据...")
    
    # 1. 直接检查数据库
    print("\n📊 检查数据库中的 Dify 实例...")
    try:
        # 连接 MongoDB
        client = AsyncIOMotorClient("mongodb://localhost:27017")
        db = client.thunderhub
        collection = db.dify_instances
        
        # 查询所有实例
        cursor = collection.find({})
        instances = []
        async for doc in cursor:
            doc['_id'] = str(doc['_id'])
            instances.append(doc)
        
        print(f"✅ 数据库中找到 {len(instances)} 个 Dify 实例:")
        for i, instance in enumerate(instances, 1):
            print(f"  {i}. {instance.get('name', 'Unknown')} - {instance.get('base_url', 'Unknown')}")
            print(f"     ID: {instance['_id']}")
            print(f"     状态: {instance.get('status', 'Unknown')}")
            print(f"     启用: {instance.get('enabled', 'Unknown')}")
            print()
        
        await client.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")
    
    # 2. 测试后端 API
    print("\n🌐 测试后端 API...")
    backend_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试获取实例列表 API
            print("📡 调用 GET /api/v1/dify/instances...")
            async with session.get(f"{backend_url}/api/v1/dify/instances") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await response.text()
                    print(f"   错误响应: {error_text}")
            
            # 测试带参数的 API
            print("\n📡 调用 GET /api/v1/dify/instances?enabled_only=false...")
            async with session.get(f"{backend_url}/api/v1/dify/instances?enabled_only=false") as response:
                print(f"   状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await response.text()
                    print(f"   错误响应: {error_text}")
                    
        except Exception as e:
            print(f"❌ API 测试失败: {str(e)}")
    
    # 3. 测试前端 API 调用
    print("\n🖥️  模拟前端 API 调用...")
    async with aiohttp.ClientSession() as session:
        try:
            # 模拟前端请求
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            async with session.get(
                f"{backend_url}/api/v1/dify/instances",
                headers=headers,
                params={"enabled_only": False}
            ) as response:
                print(f"   状态码: {response.status}")
                print(f"   响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    data = await response.json()
                    print(f"   实例数量: {len(data.get('data', []))}")
                    print(f"   完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    error_text = await response.text()
                    print(f"   错误响应: {error_text}")
                    
        except Exception as e:
            print(f"❌ 前端 API 模拟失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(debug_dify_instances())
